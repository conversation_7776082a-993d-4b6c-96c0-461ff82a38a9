{"algo_name": "td3_bc", "experiment": {"name": "test", "validate": false, "logging": {"terminal_output_to_txt": true, "log_tb": true, "log_wandb": false, "wandb_proj_name": "debug"}, "save": {"enabled": true, "every_n_seconds": null, "every_n_epochs": 20, "epochs": [], "on_best_validation": false, "on_best_rollout_return": true, "on_best_rollout_success_rate": false}, "epoch_every_n_steps": 5000, "validation_epoch_every_n_steps": 10, "env": null, "additional_envs": null, "render": false, "render_video": false, "keep_all_videos": false, "video_skip": 5, "rollout": {"enabled": true, "n": 50, "horizon": 1000, "rate": 1, "warmstart": 0, "terminate_on_success": true}}, "train": {"data": null, "output_dir": "../td3_bc_trained_models", "num_data_workers": 0, "hdf5_cache_mode": "all", "hdf5_use_swmr": true, "hdf5_load_next_obs": true, "hdf5_normalize_obs": true, "hdf5_filter_key": null, "hdf5_validation_filter_key": null, "seq_length": 1, "pad_seq_length": true, "frame_stack": 1, "pad_frame_stack": true, "dataset_keys": ["actions", "rewards", "dones"], "goal_mode": null, "cuda": true, "batch_size": 256, "num_epochs": 200, "seed": 1}, "algo": {"optim_params": {"critic": {"learning_rate": {"initial": 0.0003, "decay_factor": 0.1, "epoch_schedule": []}, "regularization": {"L2": 0.0}, "start_epoch": -1, "end_epoch": -1}, "actor": {"learning_rate": {"initial": 0.0003, "decay_factor": 0.1, "epoch_schedule": []}, "regularization": {"L2": 0.0}, "start_epoch": -1, "end_epoch": -1}}, "alpha": 2.5, "discount": 0.99, "n_step": 1, "target_tau": 0.005, "infinite_horizon": false, "critic": {"use_huber": false, "max_gradient_norm": null, "value_bounds": null, "ensemble": {"n": 2, "weight": 1.0}, "layer_dims": [256, 256]}, "actor": {"update_freq": 2, "noise_std": 0.2, "noise_clip": 0.5, "layer_dims": [256, 256]}}, "observation": {"modalities": {"obs": {"low_dim": ["flat"], "rgb": [], "depth": [], "scan": []}, "goal": {"low_dim": [], "rgb": [], "depth": [], "scan": []}}, "encoder": {"low_dim": {"core_class": null, "core_kwargs": {}, "obs_randomizer_class": null, "obs_randomizer_kwargs": {}}, "rgb": {"core_class": "VisualCore", "core_kwargs": {}, "obs_randomizer_class": null, "obs_randomizer_kwargs": {}}, "depth": {"core_class": "VisualCore", "core_kwargs": {}, "obs_randomizer_class": null, "obs_randomizer_kwargs": {}}, "scan": {"core_class": "ScanCore", "core_kwargs": {}, "obs_randomizer_class": null, "obs_randomizer_kwargs": {}}}}, "meta": {"hp_base_config_file": null, "hp_keys": [], "hp_values": []}}